# WooCommerce Product Exporter

A powerful WordPress plugin for exporting selected WooCommerce products with advanced filtering and multiple format support. Perfect for contact lens stores and complex variable products.

## Features

### 🎯 **Product Selection**
- **Bulk Selection**: Select multiple products with checkboxes
- **Advanced Filtering**: Filter by category, product type, status, stock status
- **Search Functionality**: Search products by name or SKU
- **Pagination**: Handle large product catalogs efficiently

### 📊 **Export Formats**
- **CSV**: Excel-compatible with UTF-8 BOM
- **XML**: Structured XML with CDATA support
- **JSON**: Clean JSON with nested structure

### 🔧 **Export Options**
- **Include Variations**: Export variable product variations
- **Include Images**: Export product image URLs
- **Include Categories**: Export product categories and tags
- **Include Attributes**: Export all product attributes (SPH, CYL, AXIS, etc.)

### 👁️ **Contact Lens Specific**
- **Variable Products**: Handle complex contact lens variations
- **Prescription Attributes**: SPH, CYL, AXIS, ADD, Base Curve, Diameter
- **Brand & Material**: Brand, wear time, material, color attributes
- **Inventory Management**: Stock status and quantity tracking

## Installation

### Method 1: Upload Plugin Files

1. **Download** the plugin files
2. **Upload** the `woocommerce-product-exporter` folder to `/wp-content/plugins/`
3. **Activate** the plugin through the 'Plugins' menu in WordPress
4. **Navigate** to WooCommerce → Product Exporter

### Method 2: WordPress Admin Upload

1. Go to **Plugins → Add New → Upload Plugin**
2. **Choose** the plugin ZIP file
3. **Install** and **Activate**
4. **Navigate** to WooCommerce → Product Exporter

## Requirements

- **WordPress**: 5.0 or higher
- **WooCommerce**: 5.0 or higher (tested up to 9.0)
- **PHP**: 7.4 or higher
- **Memory**: 256MB+ recommended for large exports

## WooCommerce Compatibility

✅ **Fully Compatible with Modern WooCommerce Features:**
- **HPOS (High-Performance Order Storage)**: Full compatibility declared
- **Cart & Checkout Blocks**: Compatible with new block-based checkout
- **Product Block Editor**: Works with the new product editor
- **WooCommerce 9.0+**: Tested and compatible with latest versions

## Usage Guide

### 1. **Access the Plugin**
Navigate to **WooCommerce → Product Exporter** in your WordPress admin.

### 2. **Configure Export Options**
- **Export Format**: Choose CSV, XML, or JSON
- **Include Variations**: Check to export variable product variations
- **Include Images**: Check to export product image URLs
- **Include Categories**: Check to export categories and tags
- **Include Attributes**: Check to export all product attributes

### 3. **Filter Products**
- **Category**: Filter by specific product categories
- **Product Type**: Filter by simple, variable, grouped, or external
- **Status**: Filter by published, draft, or private products
- **Stock Status**: Filter by in stock, out of stock, or backorder
- **Search**: Search by product name or SKU

### 4. **Load and Select Products**
1. Click **"Load Products"** to display filtered products
2. Use **"Select All"** or **"Deselect All"** for bulk selection
3. Or manually check individual products
4. Monitor the **selected count** in the top right

### 5. **Export Products**
1. Click **"Export Selected Products"**
2. Wait for the export to complete
3. Click the **download link** to get your file

## Export Formats

### CSV Format
- **Excel Compatible**: UTF-8 BOM for proper character encoding
- **Comprehensive Data**: All product fields, attributes, and metadata
- **WooCommerce Ready**: Can be re-imported into WooCommerce
- **Contact Lens Optimized**: Includes SPH, CYL, AXIS, ADD values

### XML Format
- **Structured Data**: Hierarchical XML structure
- **CDATA Sections**: Proper handling of HTML content
- **RSS Compatible**: Can be used for product feeds
- **Google Shopping**: Includes Google Shopping attributes

### JSON Format
- **Clean Structure**: Organized into logical sections
- **REST API Compatible**: Matches WooCommerce REST API format
- **Nested Data**: Proper handling of complex relationships
- **Developer Friendly**: Easy to parse and manipulate

## Contact Lens Features

This plugin is specifically optimized for contact lens e-commerce:

### **Prescription Attributes**
- **SPH (Spherical Power)**: -10.00 to +6.00 range
- **CYL (Cylinder)**: -0.75 to -2.25 values
- **AXIS**: 0° to 180° in 10° increments
- **ADD**: LOW/MEDIUM/HIGH categories

### **Product Information**
- **Base Curve**: 8.4, 8.6, 8.8 etc.
- **Diameter**: 14.0, 14.2, 14.5 etc.
- **Brand**: Alcon, Johnson & Johnson, CooperVision, etc.
- **Wear Time**: Daily, Weekly, Monthly
- **Material**: Silicone Hydrogel, Hydrogel, etc.
- **Color**: For colored contact lenses

### **Variable Products**
- **Parent-Child Relationships**: Proper handling of variations
- **Bulk Variations**: Export hundreds of variations efficiently
- **Attribute Inheritance**: Proper attribute mapping from parent

## File Management

### **Temporary Files**
- Export files are stored in `/wp-content/uploads/wc-product-exporter-temp/`
- Files are automatically cleaned up after 24 hours
- Direct access is prevented with .htaccess

### **Security**
- **Nonce Verification**: All AJAX requests are verified
- **Capability Checks**: Only users with `manage_woocommerce` capability
- **Input Sanitization**: All user inputs are sanitized
- **File Protection**: Export directory is protected from direct access

## Troubleshooting

### **Common Issues**

#### Export Timeout
- **Cause**: Large number of products or server limitations
- **Solution**: Reduce the number of selected products or increase PHP execution time

#### Memory Errors
- **Cause**: Insufficient PHP memory
- **Solution**: Increase PHP memory limit to 512MB or higher

#### File Download Issues
- **Cause**: Server permissions or .htaccess conflicts
- **Solution**: Check file permissions and server configuration

#### Missing Variations
- **Cause**: "Include Variations" option not checked
- **Solution**: Enable "Include Variations" in export options

### **Performance Tips**
- Export products in batches of 100-500 for best performance
- Use filters to reduce the dataset before exporting
- Export during low-traffic hours for large catalogs
- Monitor server resources during export

## Support

For support and feature requests:
- Check the plugin documentation
- Review common troubleshooting steps
- Contact your developer or WordPress administrator

## Changelog

### Version 1.0.0
- Initial release
- CSV, XML, and JSON export formats
- Advanced product filtering
- Contact lens specific features
- Variable product support
- Bulk product selection
- Security and performance optimizations

## License

This plugin is licensed under the GPL v2 or later.

---

**Perfect for contact lens stores, optical shops, and any WooCommerce store with complex variable products!**
