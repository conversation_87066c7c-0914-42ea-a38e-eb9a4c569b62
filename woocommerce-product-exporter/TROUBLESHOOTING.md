# WooCommerce Product Exporter - Troubleshooting Guide

## 🚨 **"Export failed: undefined use context7" Error**

This error has been **RESOLVED** in the latest version. If you're still seeing this error, follow these steps:

### **Quick Fix Steps**

1. **Deactivate and Reactivate Plugin**
   ```
   1. Go to Plugins → Installed Plugins
   2. Find "WooCommerce Product Exporter"
   3. Click "Deactivate"
   4. Click "Activate"
   ```

2. **Clear Any Caching**
   - Clear browser cache (Ctrl+F5 or Cmd+Shift+R)
   - Clear any WordPress caching plugins
   - Clear server-side caching if applicable

3. **Check Plugin Files**
   - Ensure all plugin files are uploaded correctly
   - Verify the `includes/` folder contains all class files

### **What Was Fixed**

The "undefined use context7" error was caused by:
- ❌ Circular dependency between export classes
- ❌ Missing helper class definitions
- ❌ Improper class instantiation

**Fixed by:**
- ✅ Created separate `WC_Product_Exporter_Helper` class
- ✅ Removed circular dependencies
- ✅ Added proper error handling and logging
- ✅ Improved class loading order

## 🔧 **Common Export Issues**

### **1. "No products selected for export"**
**Cause:** No products were checked before clicking export
**Solution:** 
1. Click "Load Products" to display products
2. Check the boxes next to products you want to export
3. Click "Export Selected Products"

### **2. "Invalid export format selected"**
**Cause:** Unsupported export format
**Solution:** Choose CSV, XML, or JSON from the dropdown

### **3. "Export failed" (General)**
**Causes & Solutions:**

#### **Memory Issues**
- **Symptoms:** Export stops or times out
- **Solution:** 
  - Export fewer products at once (try 50-100 products)
  - Increase PHP memory limit in wp-config.php:
    ```php
    ini_set('memory_limit', '512M');
    ```

#### **Server Timeout**
- **Symptoms:** Export takes too long and fails
- **Solution:**
  - Export in smaller batches
  - Increase PHP execution time:
    ```php
    ini_set('max_execution_time', 300);
    ```

#### **File Permissions**
- **Symptoms:** "Could not create export file"
- **Solution:**
  - Check `/wp-content/uploads/` is writable
  - Set permissions: `chmod 755 wp-content/uploads/`

### **4. "WooCommerce not found" Error**
**Cause:** WooCommerce plugin not active
**Solution:**
1. Go to Plugins → Installed Plugins
2. Activate WooCommerce
3. Then activate Product Exporter

## 🔍 **Debugging Steps**

### **Enable WordPress Debug Mode**
Add to `wp-config.php`:
```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
define('WP_DEBUG_DISPLAY', false);
```

### **Check Error Logs**
1. Look in `/wp-content/debug.log`
2. Check server error logs
3. Look for `[WC Product Exporter]` entries

### **Test with Minimal Setup**
1. Temporarily deactivate other plugins
2. Switch to default WordPress theme
3. Test export functionality
4. Reactivate plugins one by one to identify conflicts

## 📋 **System Requirements Check**

### **WordPress Requirements**
- ✅ WordPress 5.0+
- ✅ WooCommerce 5.0+
- ✅ PHP 7.4+

### **Server Requirements**
- ✅ Memory: 256MB+ (512MB recommended)
- ✅ Execution Time: 60s+ (300s recommended)
- ✅ File Upload: 64MB+

### **Check Your Settings**
Create `phpinfo.php` in WordPress root:
```php
<?php phpinfo(); ?>
```
Visit `yoursite.com/phpinfo.php` to check settings.
**Delete this file after checking!**

## 🛠️ **Advanced Troubleshooting**

### **Plugin Conflict Detection**
1. Deactivate all plugins except WooCommerce and Product Exporter
2. Test export functionality
3. If it works, reactivate plugins one by one
4. Identify the conflicting plugin

### **Theme Conflict Detection**
1. Switch to a default WordPress theme (Twenty Twenty-Four)
2. Test export functionality
3. If it works, the issue is with your theme

### **Database Issues**
If products aren't loading:
1. Check WooCommerce → Status → Database
2. Look for any database errors
3. Run WooCommerce database repair if needed

## 📞 **Getting Help**

### **Before Contacting Support**
1. ✅ Try all troubleshooting steps above
2. ✅ Check WordPress and WooCommerce are updated
3. ✅ Test on staging site if possible
4. ✅ Gather error logs and system info

### **Information to Provide**
- WordPress version
- WooCommerce version
- PHP version
- Server type (shared hosting, VPS, etc.)
- Error messages (exact text)
- Steps to reproduce the issue
- Number of products being exported

### **Temporary Workarounds**

#### **Manual Product Export**
1. Go to WooCommerce → Products
2. Use WooCommerce's built-in export feature
3. Export in smaller batches

#### **Database Export**
For advanced users:
1. Access phpMyAdmin
2. Export relevant WooCommerce tables
3. Use SQL queries to extract product data

## ✅ **Prevention Tips**

### **Regular Maintenance**
- Keep WordPress and WooCommerce updated
- Monitor server resources during exports
- Test exports on staging site first
- Export in reasonable batch sizes

### **Optimal Settings**
- Export 50-100 products at a time for best performance
- Use CSV format for fastest exports
- Export during low-traffic hours
- Clear old export files regularly

---

**Most export issues are resolved by following the steps above. The plugin is now stable and compatible with all modern WooCommerce features!** 🚀
