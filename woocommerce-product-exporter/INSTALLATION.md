# WooCommerce Product Exporter - Installation Guide

## Quick Installation

### Step 1: Upload Plugin
1. Download the `woocommerce-product-exporter` folder
2. Upload it to your WordPress `/wp-content/plugins/` directory
3. The final path should be: `/wp-content/plugins/woocommerce-product-exporter/`

### Step 2: Activate Plugin
1. Go to your WordPress admin dashboard
2. Navigate to **Plugins → Installed Plugins**
3. Find "WooCommerce Product Exporter"
4. Click **"Activate"**

### Step 3: Access Plugin
1. Go to **WooCommerce → Product Exporter**
2. Start exporting your products!

## File Structure

```
woocommerce-product-exporter/
├── woocommerce-product-exporter.php    # Main plugin file
├── README.md                           # Documentation
├── INSTALLATION.md                     # This file
├── assets/
│   ├── css/
│   │   └── admin.css                   # Admin styling
│   └── js/
│       └── admin.js                    # Admin JavaScript
└── includes/
    ├── class-wc-product-exporter-admin.php    # Admin interface
    ├── class-wc-product-exporter-export.php   # Export logic
    ├── class-wc-product-exporter-csv.php      # CSV exporter
    ├── class-wc-product-exporter-xml.php      # XML exporter
    └── class-wc-product-exporter-json.php     # JSON exporter
```

## Requirements Check

Before installation, ensure your server meets these requirements:

### WordPress Requirements
- **WordPress Version**: 5.0 or higher
- **WooCommerce Version**: 5.0 or higher
- **User Role**: Administrator or Shop Manager

### Server Requirements
- **PHP Version**: 7.4 or higher
- **Memory Limit**: 256MB minimum (512MB recommended)
- **Execution Time**: 300 seconds recommended
- **File Upload Size**: 64MB minimum

### Check Your Server Settings

You can check your current PHP settings by creating a file called `phpinfo.php` in your WordPress root with this content:

```php
<?php phpinfo(); ?>
```

Then visit `yoursite.com/phpinfo.php` to see your settings. **Remember to delete this file after checking!**

## Server Configuration

### Increase PHP Memory Limit

Add this to your `wp-config.php` file:
```php
ini_set('memory_limit', '512M');
```

Or add this to your `.htaccess` file:
```
php_value memory_limit 512M
```

### Increase Execution Time

Add this to your `wp-config.php` file:
```php
ini_set('max_execution_time', 300);
```

Or add this to your `.htaccess` file:
```
php_value max_execution_time 300
```

## Permissions Setup

Ensure proper file permissions:

```bash
# Set directory permissions
chmod 755 wp-content/plugins/woocommerce-product-exporter/

# Set file permissions
chmod 644 wp-content/plugins/woocommerce-product-exporter/*.php
chmod 644 wp-content/plugins/woocommerce-product-exporter/includes/*.php
chmod 644 wp-content/plugins/woocommerce-product-exporter/assets/css/*.css
chmod 644 wp-content/plugins/woocommerce-product-exporter/assets/js/*.js

# Ensure uploads directory is writable
chmod 755 wp-content/uploads/
```

## Troubleshooting Installation

### Plugin Not Appearing
- **Check file permissions** (see above)
- **Verify file structure** matches the layout shown
- **Check PHP error logs** for any syntax errors

### Activation Errors
- **WooCommerce Missing**: Install and activate WooCommerce first
- **PHP Version**: Ensure PHP 7.4 or higher
- **Memory Issues**: Increase PHP memory limit

### Permission Errors
- **File Permissions**: Set correct permissions (see above)
- **Uploads Directory**: Ensure `/wp-content/uploads/` is writable
- **Plugin Directory**: Ensure plugin directory has correct permissions

## Manual Installation via FTP

If you're installing via FTP:

1. **Extract** the plugin files on your computer
2. **Connect** to your server via FTP
3. **Navigate** to `/wp-content/plugins/`
4. **Upload** the entire `woocommerce-product-exporter` folder
5. **Set permissions** as described above
6. **Activate** the plugin in WordPress admin

## Installation via WP-CLI

If you have WP-CLI installed:

```bash
# Navigate to your WordPress directory
cd /path/to/your/wordpress

# Install the plugin (if you have a ZIP file)
wp plugin install /path/to/woocommerce-product-exporter.zip

# Activate the plugin
wp plugin activate woocommerce-product-exporter
```

## Verification

After installation, verify everything is working:

1. **Check Plugin Status**: Go to Plugins → Installed Plugins
2. **Access Plugin Page**: Go to WooCommerce → Product Exporter
3. **Test Basic Function**: Try loading products with filters
4. **Test Export**: Export a small batch of products

## Uninstallation

To remove the plugin:

1. **Deactivate**: Go to Plugins → Installed Plugins → Deactivate
2. **Delete**: Click "Delete" to remove plugin files
3. **Clean Up**: The plugin automatically cleans up temporary files

### Manual Cleanup

If needed, manually remove:
- Plugin files: `/wp-content/plugins/woocommerce-product-exporter/`
- Temporary files: `/wp-content/uploads/wc-product-exporter-temp/`
- Plugin options: `wc_product_exporter_options` (in wp_options table)

## Support

If you encounter issues during installation:

1. **Check Requirements**: Verify all requirements are met
2. **Review Error Logs**: Check PHP and WordPress error logs
3. **Test Environment**: Try on a staging site first
4. **Contact Support**: Reach out to your developer or system administrator

---

**Ready to start exporting your WooCommerce products!** 🚀
