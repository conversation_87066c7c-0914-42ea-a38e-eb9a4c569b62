# WooCommerce Product Exporter - Debug Steps

## 🔍 **Step-by-Step Debugging Guide**

Based on your description, the products are loading correctly but the export isn't working. Let's debug this step by step:

### **Step 1: Check Product Selection**

1. **Load Products**: Click "Load Products" button ✅ (This is working)
2. **Select Products**: You need to **check the checkboxes** next to the products you want to export
   - Look for small checkboxes to the left of each product
   - Click the checkboxes to select products
   - The "Selected: 0" should change to "Selected: X" where X is the number of products selected

### **Step 2: Verify Selection Counter**

- After checking product checkboxes, the counter should show "Selected: 1", "Selected: 2", etc.
- If it still shows "Selected: 0", the checkboxes aren't working properly

### **Step 3: Check Browser Console for Errors**

1. **Open Browser Developer Tools**:
   - **Chrome/Edge**: Press F12 or Ctrl+Shift+I
   - **Firefox**: Press F12 or Ctrl+Shift+K
   - **Safari**: Press Cmd+Option+I

2. **Go to Console Tab**

3. **Try to Export**: Select some products and click "Export Selected Products"

4. **Look for Error Messages** in the console

### **Step 4: Common Issues & Solutions**

#### **Issue A: Checkboxes Not Appearing**
**Symptoms**: No checkboxes visible next to products
**Solution**: 
- Check if CSS is loading properly
- Try refreshing the page
- Check if JavaScript is enabled

#### **Issue B: Checkboxes Not Working**
**Symptoms**: Can't check the boxes or counter doesn't update
**Solution**:
- Check browser console for JavaScript errors
- Try disabling other plugins temporarily
- Clear browser cache

#### **Issue C: AJAX Error**
**Symptoms**: Export button doesn't respond or shows error
**Solution**:
- Check browser console for AJAX errors
- Verify WordPress AJAX is working
- Check server error logs

### **Step 5: Manual Testing**

Try this simple test:

1. **Load Products** (working ✅)
2. **Check ONE product checkbox** 
3. **Verify counter shows "Selected: 1"**
4. **Click "Export Selected Products"**
5. **Watch browser console for any errors**

### **Step 6: Enable Debug Mode**

Add this to your `wp-config.php` file:
```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
define('WP_DEBUG_DISPLAY', false);
```

Then check `/wp-content/debug.log` for any PHP errors.

### **Step 7: Check Server Requirements**

Verify your server meets requirements:
- PHP 7.4+
- WordPress 5.0+
- WooCommerce 5.0+
- Memory: 256MB+

### **Step 8: Plugin Conflict Test**

1. **Deactivate all plugins** except WooCommerce and Product Exporter
2. **Test export functionality**
3. **If it works**, reactivate plugins one by one to find the conflict

### **Step 9: Theme Conflict Test**

1. **Switch to default WordPress theme** (Twenty Twenty-Four)
2. **Test export functionality**
3. **If it works**, the issue is with your current theme

## 🚨 **Most Likely Issues**

Based on your description, the most likely issues are:

### **1. Products Not Selected (Most Common)**
- You need to actually **check the checkboxes** next to products
- The "Selected: 0" indicates no products are currently selected
- **Solution**: Click the small checkboxes next to each product name

### **2. JavaScript Error**
- Browser console will show JavaScript errors
- **Solution**: Check console and fix any JavaScript conflicts

### **3. AJAX Permission Error**
- WordPress security preventing AJAX calls
- **Solution**: Check user permissions and nonce verification

## 📞 **What to Report Back**

Please check and report:

1. **Can you see checkboxes** next to the product names?
2. **Does the counter update** when you check boxes?
3. **Any errors in browser console** when you try to export?
4. **What happens exactly** when you click "Export Selected Products"?
5. **Your WordPress and WooCommerce versions**

## 🔧 **Quick Fix Attempts**

Try these in order:

1. **Refresh the page** and try again
2. **Clear browser cache** (Ctrl+F5)
3. **Try a different browser**
4. **Deactivate other plugins** temporarily
5. **Check if you have admin permissions**

---

**The plugin is working correctly for product loading, so the issue is likely in the selection or export process. Follow these steps to identify the exact problem!** 🔍
