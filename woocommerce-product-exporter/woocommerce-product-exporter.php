<?php
/**
 * Plugin Name: WooCommerce Product Exporter
 * Plugin URI: https://github.com/your-username/woocommerce-product-exporter
 * Description: Export selected WooCommerce products with advanced filtering and multiple format support. Perfect for contact lens and variable products.
 * Version: 1.0.0
 * Author: Your Name
 * Author URI: https://your-website.com
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: wc-product-exporter
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 * WC requires at least: 5.0
 * WC tested up to: 8.5
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('WC_PRODUCT_EXPORTER_VERSION', '1.0.0');
define('WC_PRODUCT_EXPORTER_PLUGIN_FILE', __FILE__);
define('WC_PRODUCT_EXPORTER_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('WC_PRODUCT_EXPORTER_PLUGIN_URL', plugin_dir_url(__FILE__));

/**
 * Main WooCommerce Product Exporter Class
 */
class WC_Product_Exporter {
    
    /**
     * Plugin instance
     */
    private static $instance = null;
    
    /**
     * Get plugin instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        add_action('plugins_loaded', array($this, 'init'));
    }
    
    /**
     * Initialize plugin
     */
    public function init() {
        // Check if WooCommerce is active
        if (!class_exists('WooCommerce')) {
            add_action('admin_notices', array($this, 'woocommerce_missing_notice'));
            return;
        }
        
        // Load plugin textdomain
        load_plugin_textdomain('wc-product-exporter', false, dirname(plugin_basename(__FILE__)) . '/languages');
        
        // Initialize hooks
        $this->init_hooks();
        
        // Include required files
        $this->includes();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        // Admin hooks
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
        
        // AJAX hooks
        add_action('wp_ajax_wc_export_products', array($this, 'ajax_export_products'));
        add_action('wp_ajax_wc_get_products_list', array($this, 'ajax_get_products_list'));
        
        // Plugin activation/deactivation hooks
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
    }
    
    /**
     * Include required files
     */
    private function includes() {
        require_once WC_PRODUCT_EXPORTER_PLUGIN_DIR . 'includes/class-wc-product-exporter-admin.php';
        require_once WC_PRODUCT_EXPORTER_PLUGIN_DIR . 'includes/class-wc-product-exporter-export.php';
        require_once WC_PRODUCT_EXPORTER_PLUGIN_DIR . 'includes/class-wc-product-exporter-csv.php';
        require_once WC_PRODUCT_EXPORTER_PLUGIN_DIR . 'includes/class-wc-product-exporter-xml.php';
        require_once WC_PRODUCT_EXPORTER_PLUGIN_DIR . 'includes/class-wc-product-exporter-json.php';
    }
    
    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        add_submenu_page(
            'woocommerce',
            __('Product Exporter', 'wc-product-exporter'),
            __('Product Exporter', 'wc-product-exporter'),
            'manage_woocommerce',
            'wc-product-exporter',
            array($this, 'admin_page')
        );
    }
    
    /**
     * Enqueue admin scripts and styles
     */
    public function enqueue_admin_scripts($hook) {
        if ('woocommerce_page_wc-product-exporter' !== $hook) {
            return;
        }
        
        wp_enqueue_script(
            'wc-product-exporter-admin',
            WC_PRODUCT_EXPORTER_PLUGIN_URL . 'assets/js/admin.js',
            array('jquery'),
            WC_PRODUCT_EXPORTER_VERSION,
            true
        );
        
        wp_enqueue_style(
            'wc-product-exporter-admin',
            WC_PRODUCT_EXPORTER_PLUGIN_URL . 'assets/css/admin.css',
            array(),
            WC_PRODUCT_EXPORTER_VERSION
        );
        
        // Localize script
        wp_localize_script('wc-product-exporter-admin', 'wcProductExporter', array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('wc_product_exporter_nonce'),
            'strings' => array(
                'selectProducts' => __('Please select at least one product to export.', 'wc-product-exporter'),
                'exportingProducts' => __('Exporting products...', 'wc-product-exporter'),
                'exportComplete' => __('Export completed successfully!', 'wc-product-exporter'),
                'exportError' => __('An error occurred during export.', 'wc-product-exporter'),
            )
        ));
    }
    
    /**
     * Admin page callback
     */
    public function admin_page() {
        $admin = new WC_Product_Exporter_Admin();
        $admin->display_page();
    }
    
    /**
     * AJAX handler for exporting products
     */
    public function ajax_export_products() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'wc_product_exporter_nonce')) {
            wp_die(__('Security check failed.', 'wc-product-exporter'));
        }
        
        // Check user capabilities
        if (!current_user_can('manage_woocommerce')) {
            wp_die(__('You do not have permission to perform this action.', 'wc-product-exporter'));
        }
        
        $export = new WC_Product_Exporter_Export();
        $export->handle_ajax_export();
    }
    
    /**
     * AJAX handler for getting products list
     */
    public function ajax_get_products_list() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'wc_product_exporter_nonce')) {
            wp_die(__('Security check failed.', 'wc-product-exporter'));
        }
        
        // Check user capabilities
        if (!current_user_can('manage_woocommerce')) {
            wp_die(__('You do not have permission to perform this action.', 'wc-product-exporter'));
        }
        
        $admin = new WC_Product_Exporter_Admin();
        $admin->ajax_get_products_list();
    }
    
    /**
     * Plugin activation
     */
    public function activate() {
        // Check if WooCommerce is active
        if (!class_exists('WooCommerce')) {
            deactivate_plugins(plugin_basename(__FILE__));
            wp_die(__('WooCommerce Product Exporter requires WooCommerce to be installed and active.', 'wc-product-exporter'));
        }
        
        // Create necessary database tables or options if needed
        $this->create_plugin_options();
    }
    
    /**
     * Plugin deactivation
     */
    public function deactivate() {
        // Clean up temporary files if any
        $this->cleanup_temp_files();
    }
    
    /**
     * Create plugin options
     */
    private function create_plugin_options() {
        $default_options = array(
            'default_export_format' => 'csv',
            'include_variations' => true,
            'include_images' => true,
            'include_categories' => true,
            'include_attributes' => true,
            'batch_size' => 100
        );
        
        add_option('wc_product_exporter_options', $default_options);
    }
    
    /**
     * Clean up temporary files
     */
    private function cleanup_temp_files() {
        $upload_dir = wp_upload_dir();
        $temp_dir = $upload_dir['basedir'] . '/wc-product-exporter-temp/';
        
        if (is_dir($temp_dir)) {
            $files = glob($temp_dir . '*');
            foreach ($files as $file) {
                if (is_file($file)) {
                    unlink($file);
                }
            }
        }
    }
    
    /**
     * WooCommerce missing notice
     */
    public function woocommerce_missing_notice() {
        echo '<div class="notice notice-error"><p>';
        echo __('WooCommerce Product Exporter requires WooCommerce to be installed and active.', 'wc-product-exporter');
        echo '</p></div>';
    }
}

// Initialize the plugin
WC_Product_Exporter::get_instance();
