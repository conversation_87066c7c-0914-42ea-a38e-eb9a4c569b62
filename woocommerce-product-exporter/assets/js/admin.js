/**
 * WooCommerce Product Exporter Admin JavaScript
 */

(function($) {
    'use strict';
    
    var WCProductExporter = {
        
        // Initialize the plugin
        init: function() {
            this.bindEvents();
            this.updateSelectedCount();
        },
        
        // Bind event handlers
        bindEvents: function() {
            var self = this;
            
            // Load products button
            $('#load_products').on('click', function(e) {
                e.preventDefault();
                self.loadProducts();
            });
            
            // Reset filters button
            $('#reset_filters').on('click', function(e) {
                e.preventDefault();
                self.resetFilters();
            });
            
            // Select all products
            $('#select_all').on('click', function(e) {
                e.preventDefault();
                self.selectAllProducts(true);
            });
            
            // Deselect all products
            $('#deselect_all').on('click', function(e) {
                e.preventDefault();
                self.selectAllProducts(false);
            });
            
            // Product checkbox change
            $(document).on('change', '.product-checkbox', function() {
                self.updateSelectedCount();
            });
            
            // Export form submission
            $('#wc-product-exporter-form').on('submit', function(e) {
                e.preventDefault();
                self.exportProducts();
            });
            
            // Pagination clicks
            $(document).on('click', '.pagination-link', function(e) {
                e.preventDefault();
                var page = $(this).data('page');
                self.loadProducts(page);
            });
            
            // Search on Enter key
            $('#search_products').on('keypress', function(e) {
                if (e.which === 13) {
                    e.preventDefault();
                    self.loadProducts();
                }
            });
        },
        
        // Load products based on filters
        loadProducts: function(page) {
            var self = this;
            page = page || 1;
            
            var filters = {
                category: $('#filter_category').val(),
                type: $('#filter_type').val(),
                status: $('#filter_status').val(),
                stock: $('#filter_stock').val(),
                search: $('#search_products').val()
            };
            
            var data = {
                action: 'wc_get_products_list',
                nonce: wcProductExporter.nonce,
                page: page,
                per_page: 20,
                filters: filters
            };
            
            // Show loading state
            $('#products_list').addClass('loading');
            $('#load_products').prop('disabled', true).text('Loading...');
            
            $.post(wcProductExporter.ajaxUrl, data, function(response) {
                if (response.success) {
                    self.renderProductsList(response.data.products);
                    self.renderPagination(response.data);
                } else {
                    self.showMessage('Error loading products: ' + response.data, 'error');
                }
            }).fail(function() {
                self.showMessage('Failed to load products. Please try again.', 'error');
            }).always(function() {
                $('#products_list').removeClass('loading');
                $('#load_products').prop('disabled', false).text('Load Products');
            });
        },
        
        // Render products list
        renderProductsList: function(products) {
            var $list = $('#products_list');
            
            if (!products || products.length === 0) {
                $list.html('<p class="no-products">No products found matching your criteria.</p>');
                return;
            }
            
            var html = '';
            $.each(products, function(index, product) {
                var imageHtml = product.image ? 
                    '<img src="' + product.image + '" alt="' + product.name + '" class="product-image">' :
                    '<div class="product-image"></div>';
                
                var variationsText = product.variations_count > 0 ? 
                    ' (' + product.variations_count + ' variations)' : '';
                
                html += '<div class="product-item">';
                html += '<input type="checkbox" class="product-checkbox" value="' + product.id + '">';
                html += imageHtml;
                html += '<div class="product-details">';
                html += '<div class="product-name">' + product.name + variationsText + '</div>';
                html += '<div class="product-meta">';
                html += '<span>SKU: ' + (product.sku || 'N/A') + '</span>';
                html += '<span class="product-type ' + product.type + '">' + product.type + '</span>';
                html += '<span class="product-status ' + product.status + '">' + product.status + '</span>';
                html += '<span class="stock-status ' + product.stock_status + '">' + product.stock_status + '</span>';
                html += '<span>' + product.price + '</span>';
                html += '</div>';
                html += '</div>';
                html += '</div>';
            });
            
            $list.html(html);
            this.updateSelectedCount();
        },
        
        // Render pagination
        renderPagination: function(data) {
            var $pagination = $('#products_pagination');
            
            if (data.pages <= 1) {
                $pagination.empty();
                return;
            }
            
            var html = '<div class="pagination-info">Page ' + data.current_page + ' of ' + data.pages + ' (' + data.total + ' products)</div>';
            
            if (data.current_page > 1) {
                html += '<button class="button pagination-link" data-page="' + (data.current_page - 1) + '">Previous</button>';
            }
            
            // Show page numbers (max 5)
            var startPage = Math.max(1, data.current_page - 2);
            var endPage = Math.min(data.pages, startPage + 4);
            
            for (var i = startPage; i <= endPage; i++) {
                var activeClass = i === data.current_page ? ' button-primary' : ' button-secondary';
                html += '<button class="button pagination-link' + activeClass + '" data-page="' + i + '">' + i + '</button>';
            }
            
            if (data.current_page < data.pages) {
                html += '<button class="button pagination-link" data-page="' + (data.current_page + 1) + '">Next</button>';
            }
            
            $pagination.html(html);
        },
        
        // Reset all filters
        resetFilters: function() {
            $('#filter_category').val('');
            $('#filter_type').val('');
            $('#filter_status').val('');
            $('#filter_stock').val('');
            $('#search_products').val('');
            $('#products_list').html('<p class="no-products">Click "Load Products" to display products for selection.</p>');
            $('#products_pagination').empty();
            this.updateSelectedCount();
        },
        
        // Select/deselect all products
        selectAllProducts: function(select) {
            $('.product-checkbox').prop('checked', select);
            this.updateSelectedCount();
        },
        
        // Update selected products count
        updateSelectedCount: function() {
            var count = $('.product-checkbox:checked').length;
            $('#selected_count').text(count);
        },
        
        // Export selected products
        exportProducts: function() {
            var self = this;
            var selectedProducts = [];
            
            $('.product-checkbox:checked').each(function() {
                selectedProducts.push($(this).val());
            });
            
            if (selectedProducts.length === 0) {
                alert(wcProductExporter.strings.selectProducts);
                return;
            }
            
            var exportOptions = {
                include_variations: $('#include_variations').is(':checked') ? '1' : '0',
                include_images: $('#include_images').is(':checked') ? '1' : '0',
                include_categories: $('#include_categories').is(':checked') ? '1' : '0',
                include_attributes: $('#include_attributes').is(':checked') ? '1' : '0'
            };
            
            var data = {
                action: 'wc_export_products',
                nonce: wcProductExporter.nonce,
                product_ids: selectedProducts,
                export_format: $('#export_format').val(),
                options: exportOptions
            };
            
            // Show progress
            this.showProgress(true);
            $('#export_products').prop('disabled', true);
            
            console.log('Sending export request:', data);

            $.post(wcProductExporter.ajaxUrl, data, function(response) {
                console.log('Export response:', response);
                if (response.success) {
                    self.showMessage(
                        response.data.message + ' <a href="' + response.data.download_url + '" class="download-link" target="_blank">Download File</a>',
                        'success'
                    );
                } else {
                    console.error('Export error:', response.data);
                    self.showMessage('Export failed: ' + response.data, 'error');
                }
            }).fail(function(xhr, status, error) {
                console.error('AJAX error:', xhr, status, error);
                self.showMessage('Export failed. Please try again. Error: ' + error, 'error');
            }).always(function() {
                self.showProgress(false);
                $('#export_products').prop('disabled', false);
            });
        },
        
        // Show/hide progress bar
        showProgress: function(show) {
            if (show) {
                $('#export_progress').show();
                this.animateProgress();
            } else {
                $('#export_progress').hide();
                $('.progress-fill').css('width', '0%');
            }
        },
        
        // Animate progress bar
        animateProgress: function() {
            var progress = 0;
            var interval = setInterval(function() {
                progress += Math.random() * 15;
                if (progress > 90) {
                    progress = 90;
                    clearInterval(interval);
                }
                $('.progress-fill').css('width', progress + '%');
            }, 200);
            
            // Store interval for cleanup
            this.progressInterval = interval;
        },
        
        // Show message
        showMessage: function(message, type) {
            var $message = $('.export-message');
            
            if ($message.length === 0) {
                $message = $('<div class="export-message"></div>');
                $('.wc-export-submit').append($message);
            }
            
            $message.removeClass('success error')
                   .addClass(type)
                   .html(message)
                   .show();
            
            // Auto-hide after 10 seconds for success messages
            if (type === 'success') {
                setTimeout(function() {
                    $message.fadeOut();
                }, 10000);
            }
        }
    };
    
    // Initialize when document is ready
    $(document).ready(function() {
        WCProductExporter.init();
    });
    
})(jQuery);
