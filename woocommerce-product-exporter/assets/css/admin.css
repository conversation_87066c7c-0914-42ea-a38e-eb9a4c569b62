/**
 * WooCommerce Product Exporter Admin Styles
 */

.wc-product-exporter-container {
    max-width: 1200px;
    margin: 20px 0;
}

.wc-product-exporter-container h2 {
    margin-top: 30px;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #ddd;
}

/* Export Options */
.wc-export-options {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
}

.wc-export-options .form-table th {
    width: 200px;
    padding: 15px 10px 15px 0;
}

.wc-export-options .form-table td {
    padding: 15px 10px;
}

.wc-export-options select {
    min-width: 200px;
}

/* Product Filters */
.wc-product-filters {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
}

.filter-row {
    display: flex;
    gap: 20px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.filter-column {
    flex: 1;
    min-width: 200px;
}

.filter-column label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
}

.filter-column select,
.filter-column input[type="text"] {
    width: 100%;
    max-width: 300px;
}

/* Product Selection */
.wc-product-selection {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
}

.selection-controls {
    display: flex;
    gap: 10px;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
}

.selected-count {
    margin-left: auto;
    font-weight: 600;
    color: #0073aa;
}

/* Products List */
.products-list {
    max-height: 500px;
    overflow-y: auto;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.products-list.loading {
    opacity: 0.6;
    pointer-events: none;
}

.product-item {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    border-bottom: 1px solid #eee;
    transition: background-color 0.2s;
}

.product-item:hover {
    background-color: #f8f9fa;
}

.product-item:last-child {
    border-bottom: none;
}

.product-checkbox {
    margin-right: 12px;
}

.product-image {
    width: 40px;
    height: 40px;
    margin-right: 12px;
    border-radius: 4px;
    object-fit: cover;
    background-color: #f0f0f0;
}

.product-details {
    flex: 1;
}

.product-name {
    font-weight: 600;
    margin-bottom: 4px;
    color: #23282d;
}

.product-meta {
    font-size: 12px;
    color: #666;
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.product-meta span {
    display: inline-block;
}

.product-type {
    background: #0073aa;
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 10px;
    text-transform: uppercase;
}

.product-type.variable {
    background: #00a32a;
}

.product-type.grouped {
    background: #d63638;
}

.product-type.external {
    background: #f56e28;
}

.product-status {
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 10px;
    text-transform: uppercase;
}

.product-status.publish {
    background: #00a32a;
    color: white;
}

.product-status.draft {
    background: #dba617;
    color: white;
}

.product-status.private {
    background: #666;
    color: white;
}

.stock-status {
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 10px;
    text-transform: uppercase;
}

.stock-status.instock {
    background: #00a32a;
    color: white;
}

.stock-status.outofstock {
    background: #d63638;
    color: white;
}

.stock-status.onbackorder {
    background: #f56e28;
    color: white;
}

.no-products {
    text-align: center;
    padding: 40px 20px;
    color: #666;
    font-style: italic;
}

/* Pagination */
.products-pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #eee;
}

.pagination-info {
    color: #666;
    font-size: 14px;
}

/* Export Submit */
.wc-export-submit {
    text-align: center;
    padding: 20px;
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
}

.wc-export-submit .button-primary {
    font-size: 16px;
    padding: 10px 30px;
    height: auto;
}

/* Progress Bar */
.export-progress {
    margin-top: 20px;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}

.progress-bar {
    width: 100%;
    height: 20px;
    background-color: #f0f0f0;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #0073aa, #005a87);
    width: 0%;
    transition: width 0.3s ease;
    border-radius: 10px;
}

.progress-text {
    text-align: center;
    color: #666;
    margin: 0;
}

/* Loading States */
.loading-spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #0073aa;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 8px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Success/Error Messages */
.export-message {
    padding: 12px 16px;
    border-radius: 4px;
    margin: 15px 0;
    display: none;
}

.export-message.success {
    background-color: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

.export-message.error {
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

.download-link {
    display: inline-block;
    margin-top: 10px;
    padding: 8px 16px;
    background: #0073aa;
    color: white;
    text-decoration: none;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.download-link:hover {
    background: #005a87;
    color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
    .filter-row {
        flex-direction: column;
        gap: 10px;
    }
    
    .filter-column {
        min-width: auto;
    }
    
    .selection-controls {
        flex-wrap: wrap;
        gap: 8px;
    }
    
    .selected-count {
        margin-left: 0;
        width: 100%;
        text-align: center;
    }
    
    .product-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .product-meta {
        gap: 8px;
    }
}
