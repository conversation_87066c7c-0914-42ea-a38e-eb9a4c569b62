<?php
/**
 * WooCommerce Product Exporter CSV Class
 */

if (!defined('ABSPATH')) {
    exit;
}

class WC_Product_Exporter_CSV {
    
    /**
     * Export products to CSV format
     */
    public function export($products_data) {
        if (empty($products_data)) {
            throw new Exception(__('No products data to export.', 'wc-product-exporter'));
        }
        
        $temp_dir = WC_Product_Exporter_Helper::create_temp_directory();
        $filename = WC_Product_Exporter_Helper::generate_filename('csv');
        $file_path = $temp_dir . $filename;

        // Clean old files
        WC_Product_Exporter_Helper::cleanup_old_files();
        
        // Open file for writing
        $file = fopen($file_path, 'w');
        if (!$file) {
            throw new Exception(__('Could not create export file.', 'wc-product-exporter'));
        }
        
        // Set UTF-8 BOM for proper Excel compatibility
        fwrite($file, "\xEF\xBB\xBF");
        
        // Get all possible column headers from all products
        $all_headers = array();
        foreach ($products_data as $product_data) {
            $all_headers = array_merge($all_headers, array_keys($product_data));
        }
        $all_headers = array_unique($all_headers);
        
        // Sort headers for consistent output
        $priority_headers = array(
            'ID', 'Type', 'SKU', 'Name', 'Status', 'Featured',
            'Parent ID', 'Parent SKU', 'Parent Name',
            'Short Description', 'Description',
            'Regular Price', 'Sale Price',
            'Stock Status', 'Stock Quantity',
            'Categories', 'Tags', 'Images'
        );
        
        $sorted_headers = array();
        
        // Add priority headers first
        foreach ($priority_headers as $header) {
            if (in_array($header, $all_headers)) {
                $sorted_headers[] = $header;
            }
        }
        
        // Add remaining headers
        foreach ($all_headers as $header) {
            if (!in_array($header, $sorted_headers)) {
                $sorted_headers[] = $header;
            }
        }
        
        // Write CSV header
        fputcsv($file, $sorted_headers);
        
        // Write product data
        foreach ($products_data as $product_data) {
            $row = array();
            foreach ($sorted_headers as $header) {
                $value = isset($product_data[$header]) ? $product_data[$header] : '';
                
                // Clean and format the value
                $value = $this->format_csv_value($value);
                $row[] = $value;
            }
            fputcsv($file, $row);
        }
        
        fclose($file);
        
        // Return download URL
        return WC_Product_Exporter_Helper::get_file_url($filename);
    }
    
    /**
     * Format value for CSV output
     */
    private function format_csv_value($value) {
        return WC_Product_Exporter_Helper::format_export_value($value);
    }
    
    /**
     * Get CSV headers based on WooCommerce import format
     */
    public function get_woocommerce_headers() {
        return array(
            'ID',
            'Type',
            'SKU',
            'Name',
            'Published',
            'Is featured?',
            'Visibility in catalog',
            'Short description',
            'Description',
            'Date sale price starts',
            'Date sale price ends',
            'Tax status',
            'Tax class',
            'In stock?',
            'Stock',
            'Backorders allowed?',
            'Sold individually?',
            'Weight (kg)',
            'Length (cm)',
            'Width (cm)',
            'Height (cm)',
            'Allow customer reviews?',
            'Purchase note',
            'Sale price',
            'Regular price',
            'Categories',
            'Tags',
            'Shipping class',
            'Images',
            'Download limit',
            'Download expiry days',
            'Parent',
            'Grouped products',
            'Upsells',
            'Cross-sells',
            'External URL',
            'Button text',
            'Position',
            'Attribute 1 name',
            'Attribute 1 value(s)',
            'Attribute 1 visible',
            'Attribute 1 global',
            'Attribute 2 name',
            'Attribute 2 value(s)',
            'Attribute 2 visible',
            'Attribute 2 global',
            'Attribute 3 name',
            'Attribute 3 value(s)',
            'Attribute 3 visible',
            'Attribute 3 global',
            'Attribute 4 name',
            'Attribute 4 value(s)',
            'Attribute 4 visible',
            'Attribute 4 global',
            'Attribute 5 name',
            'Attribute 5 value(s)',
            'Attribute 5 visible',
            'Attribute 5 global',
        );
    }
    
    /**
     * Export in WooCommerce import format
     */
    public function export_woocommerce_format($products_data) {
        if (empty($products_data)) {
            throw new Exception(__('No products data to export.', 'wc-product-exporter'));
        }
        
        $export_handler = new WC_Product_Exporter_Export();
        $temp_dir = $export_handler->create_temp_directory();
        $filename = $export_handler->generate_filename('csv');
        $file_path = $temp_dir . $filename;
        
        // Clean old files
        $export_handler->cleanup_old_files();
        
        // Open file for writing
        $file = fopen($file_path, 'w');
        if (!$file) {
            throw new Exception(__('Could not create export file.', 'wc-product-exporter'));
        }
        
        // Set UTF-8 BOM for proper Excel compatibility
        fwrite($file, "\xEF\xBB\xBF");
        
        // Get WooCommerce standard headers
        $headers = $this->get_woocommerce_headers();
        
        // Write CSV header
        fputcsv($file, $headers);
        
        // Write product data
        foreach ($products_data as $product_data) {
            $row = $this->map_to_woocommerce_format($product_data);
            fputcsv($file, $row);
        }
        
        fclose($file);
        
        // Return download URL
        $upload_dir = wp_upload_dir();
        $file_url = $upload_dir['baseurl'] . '/wc-product-exporter-temp/' . $filename;
        
        return $file_url;
    }
    
    /**
     * Map product data to WooCommerce import format
     */
    private function map_to_woocommerce_format($product_data) {
        $mapped = array();
        $headers = $this->get_woocommerce_headers();
        
        foreach ($headers as $header) {
            switch ($header) {
                case 'ID':
                    $mapped[] = isset($product_data['ID']) ? $product_data['ID'] : '';
                    break;
                case 'Type':
                    $mapped[] = isset($product_data['Type']) ? $product_data['Type'] : '';
                    break;
                case 'SKU':
                    $mapped[] = isset($product_data['SKU']) ? $product_data['SKU'] : '';
                    break;
                case 'Name':
                    $mapped[] = isset($product_data['Name']) ? $product_data['Name'] : '';
                    break;
                case 'Published':
                    $mapped[] = isset($product_data['Status']) && $product_data['Status'] === 'publish' ? 1 : 0;
                    break;
                case 'Is featured?':
                    $mapped[] = isset($product_data['Featured']) && $product_data['Featured'] === 'yes' ? 1 : 0;
                    break;
                case 'Short description':
                    $mapped[] = isset($product_data['Short Description']) ? $this->format_csv_value($product_data['Short Description']) : '';
                    break;
                case 'Description':
                    $mapped[] = isset($product_data['Description']) ? $this->format_csv_value($product_data['Description']) : '';
                    break;
                case 'Regular price':
                    $mapped[] = isset($product_data['Regular Price']) ? $product_data['Regular Price'] : '';
                    break;
                case 'Sale price':
                    $mapped[] = isset($product_data['Sale Price']) ? $product_data['Sale Price'] : '';
                    break;
                case 'In stock?':
                    $mapped[] = isset($product_data['Stock Status']) && $product_data['Stock Status'] === 'instock' ? 1 : 0;
                    break;
                case 'Stock':
                    $mapped[] = isset($product_data['Stock Quantity']) ? $product_data['Stock Quantity'] : '';
                    break;
                case 'Weight (kg)':
                    $mapped[] = isset($product_data['Weight']) ? $product_data['Weight'] : '';
                    break;
                case 'Length (cm)':
                    $mapped[] = isset($product_data['Length']) ? $product_data['Length'] : '';
                    break;
                case 'Width (cm)':
                    $mapped[] = isset($product_data['Width']) ? $product_data['Width'] : '';
                    break;
                case 'Height (cm)':
                    $mapped[] = isset($product_data['Height']) ? $product_data['Height'] : '';
                    break;
                case 'Categories':
                    $mapped[] = isset($product_data['Categories']) ? $product_data['Categories'] : '';
                    break;
                case 'Tags':
                    $mapped[] = isset($product_data['Tags']) ? $product_data['Tags'] : '';
                    break;
                case 'Images':
                    $mapped[] = isset($product_data['Images']) ? $product_data['Images'] : '';
                    break;
                case 'Parent':
                    $mapped[] = isset($product_data['Parent SKU']) ? $product_data['Parent SKU'] : '';
                    break;
                default:
                    $mapped[] = '';
                    break;
            }
        }
        
        return $mapped;
    }
}
