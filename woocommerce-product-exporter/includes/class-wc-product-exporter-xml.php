<?php
/**
 * WooCommerce Product Exporter XML Class
 */

if (!defined('ABSPATH')) {
    exit;
}

class WC_Product_Exporter_XML {
    
    /**
     * Export products to XML format
     */
    public function export($products_data) {
        if (empty($products_data)) {
            throw new Exception(__('No products data to export.', 'wc-product-exporter'));
        }
        
        $temp_dir = WC_Product_Exporter_Helper::create_temp_directory();
        $filename = WC_Product_Exporter_Helper::generate_filename('xml');
        $file_path = $temp_dir . $filename;

        // Clean old files
        WC_Product_Exporter_Helper::cleanup_old_files();
        
        // Create XML document
        $xml = new DOMDocument('1.0', 'UTF-8');
        $xml->formatOutput = true;
        
        // Create root element
        $root = $xml->createElement('products');
        $root->setAttribute('exported_at', date('Y-m-d H:i:s'));
        $root->setAttribute('total_products', count($products_data));
        $xml->appendChild($root);
        
        // Add products
        foreach ($products_data as $product_data) {
            $product_element = $xml->createElement('product');
            
            foreach ($product_data as $key => $value) {
                $element_name = $this->sanitize_xml_element_name($key);
                $element = $xml->createElement($element_name);
                
                if (is_array($value)) {
                    $value = implode(', ', $value);
                }
                
                // Handle CDATA for text content
                if ($this->needs_cdata($value)) {
                    $cdata = $xml->createCDATASection($value);
                    $element->appendChild($cdata);
                } else {
                    $element->nodeValue = htmlspecialchars($value, ENT_XML1, 'UTF-8');
                }
                
                $product_element->appendChild($element);
            }
            
            $root->appendChild($product_element);
        }
        
        // Save XML file
        if (!$xml->save($file_path)) {
            throw new Exception(__('Could not create XML export file.', 'wc-product-exporter'));
        }
        
        // Return download URL
        return WC_Product_Exporter_Helper::get_file_url($filename);
    }
    
    /**
     * Sanitize XML element name
     */
    private function sanitize_xml_element_name($name) {
        // Replace spaces and special characters with underscores
        $name = preg_replace('/[^a-zA-Z0-9_-]/', '_', $name);
        
        // Ensure it starts with a letter or underscore
        if (!preg_match('/^[a-zA-Z_]/', $name)) {
            $name = 'field_' . $name;
        }
        
        return strtolower($name);
    }
    
    /**
     * Check if value needs CDATA section
     */
    private function needs_cdata($value) {
        return (
            strpos($value, '<') !== false ||
            strpos($value, '>') !== false ||
            strpos($value, '&') !== false ||
            strpos($value, '"') !== false ||
            strpos($value, "'") !== false ||
            strlen($value) > 100
        );
    }
    
    /**
     * Export in RSS format for feeds
     */
    public function export_rss_format($products_data) {
        if (empty($products_data)) {
            throw new Exception(__('No products data to export.', 'wc-product-exporter'));
        }
        
        $export_handler = new WC_Product_Exporter_Export();
        $temp_dir = $export_handler->create_temp_directory();
        $filename = $export_handler->generate_filename('xml');
        $file_path = $temp_dir . $filename;
        
        // Clean old files
        $export_handler->cleanup_old_files();
        
        // Create XML document
        $xml = new DOMDocument('1.0', 'UTF-8');
        $xml->formatOutput = true;
        
        // Create RSS root
        $rss = $xml->createElement('rss');
        $rss->setAttribute('version', '2.0');
        $rss->setAttribute('xmlns:g', 'http://base.google.com/ns/1.0');
        $xml->appendChild($rss);
        
        // Create channel
        $channel = $xml->createElement('channel');
        $rss->appendChild($channel);
        
        // Channel info
        $title = $xml->createElement('title', get_bloginfo('name') . ' - Product Feed');
        $channel->appendChild($title);
        
        $link = $xml->createElement('link', home_url());
        $channel->appendChild($link);
        
        $description = $xml->createElement('description', 'Product feed from ' . get_bloginfo('name'));
        $channel->appendChild($description);
        
        // Add products as items
        foreach ($products_data as $product_data) {
            $item = $xml->createElement('item');
            
            // Basic item elements
            $item_title = $xml->createElement('title');
            $item_title->appendChild($xml->createCDATASection($product_data['Name']));
            $item->appendChild($item_title);
            
            if (!empty($product_data['Description'])) {
                $item_description = $xml->createElement('description');
                $item_description->appendChild($xml->createCDATASection($product_data['Description']));
                $item->appendChild($item_description);
            }
            
            // Product URL
            $product_url = get_permalink($product_data['ID']);
            if ($product_url) {
                $item_link = $xml->createElement('link', $product_url);
                $item->appendChild($item_link);
                
                $item_guid = $xml->createElement('guid', $product_url);
                $item_guid->setAttribute('isPermaLink', 'true');
                $item->appendChild($item_guid);
            }
            
            // Google Shopping attributes
            if (!empty($product_data['SKU'])) {
                $g_id = $xml->createElement('g:id', $product_data['SKU']);
                $item->appendChild($g_id);
            }
            
            if (!empty($product_data['Regular Price'])) {
                $g_price = $xml->createElement('g:price', $product_data['Regular Price'] . ' RSD');
                $item->appendChild($g_price);
            }
            
            if (!empty($product_data['Categories'])) {
                $g_category = $xml->createElement('g:product_type');
                $g_category->appendChild($xml->createCDATASection($product_data['Categories']));
                $item->appendChild($g_category);
            }
            
            if (!empty($product_data['Stock Status'])) {
                $availability = $product_data['Stock Status'] === 'instock' ? 'in stock' : 'out of stock';
                $g_availability = $xml->createElement('g:availability', $availability);
                $item->appendChild($g_availability);
            }
            
            if (!empty($product_data['Images'])) {
                $images = explode(', ', $product_data['Images']);
                if (!empty($images[0])) {
                    $g_image = $xml->createElement('g:image_link', $images[0]);
                    $item->appendChild($g_image);
                }
            }
            
            $channel->appendChild($item);
        }
        
        // Save XML file
        if (!$xml->save($file_path)) {
            throw new Exception(__('Could not create RSS export file.', 'wc-product-exporter'));
        }
        
        // Return download URL
        $upload_dir = wp_upload_dir();
        $file_url = $upload_dir['baseurl'] . '/wc-product-exporter-temp/' . $filename;
        
        return $file_url;
    }
}
