<?php
/**
 * WooCommerce Product Exporter Helper Class
 */

if (!defined('ABSPATH')) {
    exit;
}

class WC_Product_Exporter_Helper {
    
    /**
     * Create temporary directory for exports
     */
    public static function create_temp_directory() {
        $upload_dir = wp_upload_dir();
        $temp_dir = $upload_dir['basedir'] . '/wc-product-exporter-temp/';
        
        if (!file_exists($temp_dir)) {
            wp_mkdir_p($temp_dir);
            
            // Create .htaccess to prevent direct access
            $htaccess_content = "Options -Indexes\n<Files *.php>\nOrder allow,deny\nDeny from all\n</Files>";
            file_put_contents($temp_dir . '.htaccess', $htaccess_content);
        }
        
        return $temp_dir;
    }
    
    /**
     * Generate unique filename
     */
    public static function generate_filename($format) {
        $timestamp = date('Y-m-d_H-i-s');
        $random = wp_generate_password(8, false);
        return "wc-products-export_{$timestamp}_{$random}.{$format}";
    }
    
    /**
     * Clean old export files
     */
    public static function cleanup_old_files() {
        $temp_dir = self::create_temp_directory();
        $files = glob($temp_dir . 'wc-products-export_*');
        
        foreach ($files as $file) {
            if (is_file($file) && (time() - filemtime($file)) > (24 * 60 * 60)) { // 24 hours
                unlink($file);
            }
        }
    }
    
    /**
     * Get file URL from path
     */
    public static function get_file_url($filename) {
        $upload_dir = wp_upload_dir();
        return $upload_dir['baseurl'] . '/wc-product-exporter-temp/' . $filename;
    }
    
    /**
     * Format value for export output
     */
    public static function format_export_value($value) {
        if (is_array($value)) {
            $value = implode(', ', $value);
        }
        
        // Remove HTML tags
        $value = wp_strip_all_tags($value);
        
        // Clean up whitespace
        $value = trim(preg_replace('/\s+/', ' ', $value));
        
        // Handle special characters
        $value = html_entity_decode($value, ENT_QUOTES, 'UTF-8');
        
        return $value;
    }
    
    /**
     * Validate export format
     */
    public static function validate_export_format($format) {
        $allowed_formats = array('csv', 'xml', 'json');
        return in_array($format, $allowed_formats);
    }
    
    /**
     * Get memory usage info
     */
    public static function get_memory_info() {
        return array(
            'current' => memory_get_usage(true),
            'peak' => memory_get_peak_usage(true),
            'limit' => ini_get('memory_limit')
        );
    }
    
    /**
     * Log export activity
     */
    public static function log_export($message, $level = 'info') {
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('[WC Product Exporter] ' . $message);
        }
    }
}
