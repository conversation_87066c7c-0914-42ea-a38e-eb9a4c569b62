<?php
/**
 * WooCommerce Product Exporter Admin Class
 */

if (!defined('ABSPATH')) {
    exit;
}

class WC_Product_Exporter_Admin {
    
    /**
     * Display the admin page
     */
    public function display_page() {
        ?>
        <div class="wrap">
            <h1><?php echo esc_html(get_admin_page_title()); ?></h1>
            
            <div class="wc-product-exporter-container">
                <!-- Export Form -->
                <form id="wc-product-exporter-form" method="post">
                    <?php wp_nonce_field('wc_product_exporter_nonce', 'wc_product_exporter_nonce'); ?>
                    
                    <!-- Export Options -->
                    <div class="wc-export-options">
                        <h2><?php _e('Export Options', 'wc-product-exporter'); ?></h2>
                        
                        <table class="form-table">
                            <tr>
                                <th scope="row">
                                    <label for="export_format"><?php _e('Export Format', 'wc-product-exporter'); ?></label>
                                </th>
                                <td>
                                    <select name="export_format" id="export_format">
                                        <option value="csv"><?php _e('CSV', 'wc-product-exporter'); ?></option>
                                        <option value="xml"><?php _e('XML', 'wc-product-exporter'); ?></option>
                                        <option value="json"><?php _e('JSON', 'wc-product-exporter'); ?></option>
                                    </select>
                                    <p class="description"><?php _e('Choose the format for your export file.', 'wc-product-exporter'); ?></p>
                                </td>
                            </tr>
                            
                            <tr>
                                <th scope="row">
                                    <label for="include_variations"><?php _e('Include Variations', 'wc-product-exporter'); ?></label>
                                </th>
                                <td>
                                    <input type="checkbox" name="include_variations" id="include_variations" value="1" checked>
                                    <p class="description"><?php _e('Include product variations in the export.', 'wc-product-exporter'); ?></p>
                                </td>
                            </tr>
                            
                            <tr>
                                <th scope="row">
                                    <label for="include_images"><?php _e('Include Images', 'wc-product-exporter'); ?></label>
                                </th>
                                <td>
                                    <input type="checkbox" name="include_images" id="include_images" value="1" checked>
                                    <p class="description"><?php _e('Include product image URLs in the export.', 'wc-product-exporter'); ?></p>
                                </td>
                            </tr>
                            
                            <tr>
                                <th scope="row">
                                    <label for="include_categories"><?php _e('Include Categories', 'wc-product-exporter'); ?></label>
                                </th>
                                <td>
                                    <input type="checkbox" name="include_categories" id="include_categories" value="1" checked>
                                    <p class="description"><?php _e('Include product categories in the export.', 'wc-product-exporter'); ?></p>
                                </td>
                            </tr>
                            
                            <tr>
                                <th scope="row">
                                    <label for="include_attributes"><?php _e('Include Attributes', 'wc-product-exporter'); ?></label>
                                </th>
                                <td>
                                    <input type="checkbox" name="include_attributes" id="include_attributes" value="1" checked>
                                    <p class="description"><?php _e('Include product attributes (SPH, CYL, AXIS, etc.) in the export.', 'wc-product-exporter'); ?></p>
                                </td>
                            </tr>
                        </table>
                    </div>
                    
                    <!-- Product Filters -->
                    <div class="wc-product-filters">
                        <h2><?php _e('Product Filters', 'wc-product-exporter'); ?></h2>
                        
                        <div class="filter-row">
                            <div class="filter-column">
                                <label for="filter_category"><?php _e('Category', 'wc-product-exporter'); ?></label>
                                <select name="filter_category" id="filter_category">
                                    <option value=""><?php _e('All Categories', 'wc-product-exporter'); ?></option>
                                    <?php
                                    $categories = get_terms(array(
                                        'taxonomy' => 'product_cat',
                                        'hide_empty' => false,
                                    ));
                                    foreach ($categories as $category) {
                                        echo '<option value="' . esc_attr($category->term_id) . '">' . esc_html($category->name) . '</option>';
                                    }
                                    ?>
                                </select>
                            </div>
                            
                            <div class="filter-column">
                                <label for="filter_type"><?php _e('Product Type', 'wc-product-exporter'); ?></label>
                                <select name="filter_type" id="filter_type">
                                    <option value=""><?php _e('All Types', 'wc-product-exporter'); ?></option>
                                    <option value="simple"><?php _e('Simple', 'wc-product-exporter'); ?></option>
                                    <option value="variable"><?php _e('Variable', 'wc-product-exporter'); ?></option>
                                    <option value="grouped"><?php _e('Grouped', 'wc-product-exporter'); ?></option>
                                    <option value="external"><?php _e('External', 'wc-product-exporter'); ?></option>
                                </select>
                            </div>
                            
                            <div class="filter-column">
                                <label for="filter_status"><?php _e('Status', 'wc-product-exporter'); ?></label>
                                <select name="filter_status" id="filter_status">
                                    <option value=""><?php _e('All Statuses', 'wc-product-exporter'); ?></option>
                                    <option value="publish"><?php _e('Published', 'wc-product-exporter'); ?></option>
                                    <option value="draft"><?php _e('Draft', 'wc-product-exporter'); ?></option>
                                    <option value="private"><?php _e('Private', 'wc-product-exporter'); ?></option>
                                </select>
                            </div>
                            
                            <div class="filter-column">
                                <label for="filter_stock"><?php _e('Stock Status', 'wc-product-exporter'); ?></label>
                                <select name="filter_stock" id="filter_stock">
                                    <option value=""><?php _e('All Stock Statuses', 'wc-product-exporter'); ?></option>
                                    <option value="instock"><?php _e('In Stock', 'wc-product-exporter'); ?></option>
                                    <option value="outofstock"><?php _e('Out of Stock', 'wc-product-exporter'); ?></option>
                                    <option value="onbackorder"><?php _e('On Backorder', 'wc-product-exporter'); ?></option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="filter-row">
                            <div class="filter-column">
                                <label for="search_products"><?php _e('Search Products', 'wc-product-exporter'); ?></label>
                                <input type="text" name="search_products" id="search_products" placeholder="<?php _e('Search by name or SKU...', 'wc-product-exporter'); ?>">
                            </div>
                            
                            <div class="filter-column">
                                <button type="button" id="load_products" class="button button-secondary">
                                    <?php _e('Load Products', 'wc-product-exporter'); ?>
                                </button>
                                <button type="button" id="reset_filters" class="button button-secondary">
                                    <?php _e('Reset Filters', 'wc-product-exporter'); ?>
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Product Selection -->
                    <div class="wc-product-selection">
                        <h2><?php _e('Select Products to Export', 'wc-product-exporter'); ?></h2>
                        
                        <div class="selection-controls">
                            <button type="button" id="select_all" class="button button-secondary">
                                <?php _e('Select All', 'wc-product-exporter'); ?>
                            </button>
                            <button type="button" id="deselect_all" class="button button-secondary">
                                <?php _e('Deselect All', 'wc-product-exporter'); ?>
                            </button>
                            <span class="selected-count">
                                <?php _e('Selected: ', 'wc-product-exporter'); ?><span id="selected_count">0</span>
                            </span>
                        </div>
                        
                        <div id="products_list" class="products-list">
                            <p class="no-products"><?php _e('Click "Load Products" to display products for selection.', 'wc-product-exporter'); ?></p>
                        </div>
                        
                        <div id="products_pagination" class="products-pagination"></div>
                    </div>
                    
                    <!-- Export Button -->
                    <div class="wc-export-submit">
                        <button type="submit" id="export_products" class="button button-primary button-large">
                            <?php _e('Export Selected Products', 'wc-product-exporter'); ?>
                        </button>
                        <div id="export_progress" class="export-progress" style="display: none;">
                            <div class="progress-bar">
                                <div class="progress-fill"></div>
                            </div>
                            <p class="progress-text"><?php _e('Preparing export...', 'wc-product-exporter'); ?></p>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        <?php
    }
    
    /**
     * AJAX handler for getting products list
     */
    public function ajax_get_products_list() {
        $page = isset($_POST['page']) ? intval($_POST['page']) : 1;
        $per_page = isset($_POST['per_page']) ? intval($_POST['per_page']) : 20;
        $filters = isset($_POST['filters']) ? $_POST['filters'] : array();

        // Use modern WooCommerce methods for better compatibility
        $args = array(
            'status' => 'any',
            'limit' => $per_page,
            'offset' => ($page - 1) * $per_page,
            'return' => 'ids',
        );

        // Apply filters using WooCommerce methods
        if (!empty($filters['category'])) {
            $args['category'] = array(intval($filters['category']));
        }

        if (!empty($filters['type'])) {
            $args['type'] = sanitize_text_field($filters['type']);
        }

        if (!empty($filters['status'])) {
            $args['status'] = sanitize_text_field($filters['status']);
        }

        if (!empty($filters['stock'])) {
            $args['stock_status'] = sanitize_text_field($filters['stock']);
        }

        if (!empty($filters['search'])) {
            $args['search'] = sanitize_text_field($filters['search']);
        }

        // Get products using WooCommerce wc_get_products function
        try {
            $product_ids = wc_get_products($args);

            // Get total count for pagination
            $count_args = $args;
            $count_args['limit'] = -1;
            $count_args['return'] = 'ids';
            $total_products = wc_get_products($count_args);
            $total_count = count($total_products);

            $products = array();

            foreach ($product_ids as $product_id) {
                $product = wc_get_product($product_id);

                if ($product) {
                    $products[] = array(
                        'id' => $product->get_id(),
                        'name' => $product->get_name(),
                        'sku' => $product->get_sku(),
                        'type' => $product->get_type(),
                        'status' => $product->get_status(),
                        'stock_status' => $product->get_stock_status(),
                        'price' => $product->get_price_html(),
                        'image' => wp_get_attachment_image_url($product->get_image_id(), 'thumbnail'),
                        'variations_count' => $product->is_type('variable') ? count($product->get_children()) : 0,
                    );
                }
            }
        } catch (Exception $e) {
            wp_send_json_error(__('Error loading products: ', 'wc-product-exporter') . $e->getMessage());
            return;
        }
        
        wp_send_json_success(array(
            'products' => $products,
            'total' => $total_count,
            'pages' => ceil($total_count / $per_page),
            'current_page' => $page,
        ));
    }
}
