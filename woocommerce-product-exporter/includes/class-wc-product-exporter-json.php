<?php
/**
 * WooCommerce Product Exporter JSON Class
 */

if (!defined('ABSPATH')) {
    exit;
}

class WC_Product_Exporter_JSON {
    
    /**
     * Export products to JSON format
     */
    public function export($products_data) {
        if (empty($products_data)) {
            throw new Exception(__('No products data to export.', 'wc-product-exporter'));
        }
        
        $temp_dir = WC_Product_Exporter_Helper::create_temp_directory();
        $filename = WC_Product_Exporter_Helper::generate_filename('json');
        $file_path = $temp_dir . $filename;

        // Clean old files
        WC_Product_Exporter_Helper::cleanup_old_files();
        
        // Prepare JSON data
        $json_data = array(
            'export_info' => array(
                'exported_at' => date('Y-m-d H:i:s'),
                'total_products' => count($products_data),
                'site_url' => home_url(),
                'site_name' => get_bloginfo('name'),
                'woocommerce_version' => WC()->version,
                'plugin_version' => WC_PRODUCT_EXPORTER_VERSION,
            ),
            'products' => $this->format_products_for_json($products_data)
        );
        
        // Convert to JSON with pretty printing
        $json_string = wp_json_encode($json_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        
        if ($json_string === false) {
            throw new Exception(__('Could not encode products data to JSON.', 'wc-product-exporter'));
        }
        
        // Save JSON file
        if (file_put_contents($file_path, $json_string) === false) {
            throw new Exception(__('Could not create JSON export file.', 'wc-product-exporter'));
        }
        
        // Return download URL
        return WC_Product_Exporter_Helper::get_file_url($filename);
    }
    
    /**
     * Format products data for JSON export
     */
    private function format_products_for_json($products_data) {
        $formatted_products = array();
        
        foreach ($products_data as $product_data) {
            $formatted_product = array();
            
            // Basic product information
            $formatted_product['basic_info'] = array(
                'id' => isset($product_data['ID']) ? intval($product_data['ID']) : null,
                'type' => isset($product_data['Type']) ? $product_data['Type'] : '',
                'sku' => isset($product_data['SKU']) ? $product_data['SKU'] : '',
                'name' => isset($product_data['Name']) ? $product_data['Name'] : '',
                'status' => isset($product_data['Status']) ? $product_data['Status'] : '',
                'featured' => isset($product_data['Featured']) ? ($product_data['Featured'] === 'yes') : false,
            );
            
            // Parent information (for variations)
            if (isset($product_data['Parent ID'])) {
                $formatted_product['parent_info'] = array(
                    'parent_id' => intval($product_data['Parent ID']),
                    'parent_sku' => isset($product_data['Parent SKU']) ? $product_data['Parent SKU'] : '',
                    'parent_name' => isset($product_data['Parent Name']) ? $product_data['Parent Name'] : '',
                );
            }
            
            // Content
            $formatted_product['content'] = array(
                'short_description' => isset($product_data['Short Description']) ? wp_strip_all_tags($product_data['Short Description']) : '',
                'description' => isset($product_data['Description']) ? wp_strip_all_tags($product_data['Description']) : '',
            );
            
            // Pricing
            $formatted_product['pricing'] = array(
                'regular_price' => isset($product_data['Regular Price']) ? floatval($product_data['Regular Price']) : null,
                'sale_price' => isset($product_data['Sale Price']) ? floatval($product_data['Sale Price']) : null,
            );
            
            // Inventory
            $formatted_product['inventory'] = array(
                'stock_status' => isset($product_data['Stock Status']) ? $product_data['Stock Status'] : '',
                'stock_quantity' => isset($product_data['Stock Quantity']) ? intval($product_data['Stock Quantity']) : null,
            );
            
            // Dimensions
            $formatted_product['dimensions'] = array(
                'weight' => isset($product_data['Weight']) ? floatval($product_data['Weight']) : null,
                'length' => isset($product_data['Length']) ? floatval($product_data['Length']) : null,
                'width' => isset($product_data['Width']) ? floatval($product_data['Width']) : null,
                'height' => isset($product_data['Height']) ? floatval($product_data['Height']) : null,
            );
            
            // Categories and tags
            $formatted_product['taxonomy'] = array(
                'categories' => isset($product_data['Categories']) ? explode(', ', $product_data['Categories']) : array(),
                'tags' => isset($product_data['Tags']) ? explode(', ', $product_data['Tags']) : array(),
            );
            
            // Images
            if (isset($product_data['Images'])) {
                $formatted_product['images'] = explode(', ', $product_data['Images']);
            } else {
                $formatted_product['images'] = array();
            }
            
            // Attributes
            $formatted_product['attributes'] = array();
            $formatted_product['variations'] = array();
            $formatted_product['contact_lens_attributes'] = array();
            
            foreach ($product_data as $key => $value) {
                if (strpos($key, 'Attribute: ') === 0) {
                    $attr_name = str_replace('Attribute: ', '', $key);
                    $formatted_product['attributes'][$attr_name] = explode(', ', $value);
                } elseif (strpos($key, 'Variation: ') === 0) {
                    $var_name = str_replace('Variation: ', '', $key);
                    $formatted_product['variations'][$var_name] = $value;
                } elseif (in_array($key, array('SPH', 'CYL', 'AXIS', 'ADD', 'Base Curve', 'Diameter', 'Brand', 'Wear Time', 'Material', 'Color'))) {
                    $formatted_product['contact_lens_attributes'][strtolower(str_replace(' ', '_', $key))] = $value;
                }
            }
            
            // Custom fields/meta
            $formatted_product['custom_fields'] = array();
            foreach ($product_data as $key => $value) {
                if (strpos($key, 'Meta: ') === 0) {
                    $meta_key = str_replace('Meta: ', '', $key);
                    $formatted_product['custom_fields'][$meta_key] = $value;
                }
            }
            
            // Dates
            $formatted_product['dates'] = array(
                'created' => isset($product_data['Date Created']) ? $product_data['Date Created'] : '',
                'modified' => isset($product_data['Date Modified']) ? $product_data['Date Modified'] : '',
            );
            
            $formatted_products[] = $formatted_product;
        }
        
        return $formatted_products;
    }
    
    /**
     * Export in WooCommerce REST API format
     */
    public function export_rest_api_format($products_data) {
        if (empty($products_data)) {
            throw new Exception(__('No products data to export.', 'wc-product-exporter'));
        }
        
        $export_handler = new WC_Product_Exporter_Export();
        $temp_dir = $export_handler->create_temp_directory();
        $filename = $export_handler->generate_filename('json');
        $file_path = $temp_dir . $filename;
        
        // Clean old files
        $export_handler->cleanup_old_files();
        
        // Format products in WooCommerce REST API structure
        $api_products = array();
        
        foreach ($products_data as $product_data) {
            $api_product = array(
                'id' => isset($product_data['ID']) ? intval($product_data['ID']) : 0,
                'name' => isset($product_data['Name']) ? $product_data['Name'] : '',
                'slug' => isset($product_data['Name']) ? sanitize_title($product_data['Name']) : '',
                'type' => isset($product_data['Type']) ? $product_data['Type'] : 'simple',
                'status' => isset($product_data['Status']) ? $product_data['Status'] : 'publish',
                'featured' => isset($product_data['Featured']) ? ($product_data['Featured'] === 'yes') : false,
                'catalog_visibility' => 'visible',
                'description' => isset($product_data['Description']) ? $product_data['Description'] : '',
                'short_description' => isset($product_data['Short Description']) ? $product_data['Short Description'] : '',
                'sku' => isset($product_data['SKU']) ? $product_data['SKU'] : '',
                'price' => isset($product_data['Regular Price']) ? $product_data['Regular Price'] : '',
                'regular_price' => isset($product_data['Regular Price']) ? $product_data['Regular Price'] : '',
                'sale_price' => isset($product_data['Sale Price']) ? $product_data['Sale Price'] : '',
                'stock_status' => isset($product_data['Stock Status']) ? $product_data['Stock Status'] : 'instock',
                'stock_quantity' => isset($product_data['Stock Quantity']) ? intval($product_data['Stock Quantity']) : null,
                'weight' => isset($product_data['Weight']) ? $product_data['Weight'] : '',
                'dimensions' => array(
                    'length' => isset($product_data['Length']) ? $product_data['Length'] : '',
                    'width' => isset($product_data['Width']) ? $product_data['Width'] : '',
                    'height' => isset($product_data['Height']) ? $product_data['Height'] : '',
                ),
                'categories' => array(),
                'tags' => array(),
                'images' => array(),
                'attributes' => array(),
                'variations' => array(),
            );
            
            // Process categories
            if (isset($product_data['Categories'])) {
                $categories = explode(', ', $product_data['Categories']);
                foreach ($categories as $category) {
                    $api_product['categories'][] = array('name' => trim($category));
                }
            }
            
            // Process tags
            if (isset($product_data['Tags'])) {
                $tags = explode(', ', $product_data['Tags']);
                foreach ($tags as $tag) {
                    $api_product['tags'][] = array('name' => trim($tag));
                }
            }
            
            // Process images
            if (isset($product_data['Images'])) {
                $images = explode(', ', $product_data['Images']);
                foreach ($images as $index => $image_url) {
                    if (!empty(trim($image_url))) {
                        $api_product['images'][] = array(
                            'src' => trim($image_url),
                            'position' => $index
                        );
                    }
                }
            }
            
            // Process attributes
            foreach ($product_data as $key => $value) {
                if (strpos($key, 'Attribute: ') === 0) {
                    $attr_name = str_replace('Attribute: ', '', $key);
                    $api_product['attributes'][] = array(
                        'name' => $attr_name,
                        'options' => explode(', ', $value),
                        'visible' => true,
                        'variation' => false,
                    );
                }
            }
            
            $api_products[] = $api_product;
        }
        
        // Convert to JSON with pretty printing
        $json_string = wp_json_encode($api_products, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        
        if ($json_string === false) {
            throw new Exception(__('Could not encode products data to JSON.', 'wc-product-exporter'));
        }
        
        // Save JSON file
        if (file_put_contents($file_path, $json_string) === false) {
            throw new Exception(__('Could not create JSON export file.', 'wc-product-exporter'));
        }
        
        // Return download URL
        $upload_dir = wp_upload_dir();
        $file_url = $upload_dir['baseurl'] . '/wc-product-exporter-temp/' . $filename;
        
        return $file_url;
    }
}
