<?php
/**
 * WooCommerce Product Exporter Export Class
 */

if (!defined('ABSPATH')) {
    exit;
}

class WC_Product_Exporter_Export {
    
    /**
     * Handle AJAX export request
     */
    public function handle_ajax_export() {
        $product_ids = isset($_POST['product_ids']) ? array_map('intval', $_POST['product_ids']) : array();
        $export_format = isset($_POST['export_format']) ? sanitize_text_field($_POST['export_format']) : 'csv';
        $options = isset($_POST['options']) ? $_POST['options'] : array();
        
        if (empty($product_ids)) {
            wp_send_json_error(__('No products selected for export.', 'wc-product-exporter'));
        }
        
        // Sanitize options
        $export_options = array(
            'include_variations' => isset($options['include_variations']) && $options['include_variations'] === '1',
            'include_images' => isset($options['include_images']) && $options['include_images'] === '1',
            'include_categories' => isset($options['include_categories']) && $options['include_categories'] === '1',
            'include_attributes' => isset($options['include_attributes']) && $options['include_attributes'] === '1',
        );
        
        try {
            $file_url = $this->export_products($product_ids, $export_format, $export_options);
            
            wp_send_json_success(array(
                'message' => __('Export completed successfully!', 'wc-product-exporter'),
                'download_url' => $file_url,
                'products_count' => count($product_ids),
            ));
            
        } catch (Exception $e) {
            wp_send_json_error($e->getMessage());
        }
    }
    
    /**
     * Export products to specified format
     */
    public function export_products($product_ids, $format, $options) {
        $products_data = $this->get_products_data($product_ids, $options);
        
        switch ($format) {
            case 'csv':
                $exporter = new WC_Product_Exporter_CSV();
                break;
            case 'xml':
                $exporter = new WC_Product_Exporter_XML();
                break;
            case 'json':
                $exporter = new WC_Product_Exporter_JSON();
                break;
            default:
                throw new Exception(__('Invalid export format.', 'wc-product-exporter'));
        }
        
        return $exporter->export($products_data);
    }
    
    /**
     * Get products data for export
     */
    private function get_products_data($product_ids, $options) {
        $products_data = array();
        
        foreach ($product_ids as $product_id) {
            $product = wc_get_product($product_id);
            
            if (!$product) {
                continue;
            }
            
            $product_data = $this->get_product_data($product, $options);
            $products_data[] = $product_data;
            
            // Include variations if requested and product is variable
            if ($options['include_variations'] && $product->is_type('variable')) {
                $variations = $product->get_children();
                
                foreach ($variations as $variation_id) {
                    $variation = wc_get_product($variation_id);
                    if ($variation) {
                        $variation_data = $this->get_product_data($variation, $options, $product);
                        $products_data[] = $variation_data;
                    }
                }
            }
        }
        
        return $products_data;
    }
    
    /**
     * Get individual product data
     */
    private function get_product_data($product, $options, $parent_product = null) {
        $data = array(
            'ID' => $product->get_id(),
            'Type' => $product->get_type(),
            'SKU' => $product->get_sku(),
            'Name' => $product->get_name(),
            'Status' => $product->get_status(),
            'Featured' => $product->is_featured() ? 'yes' : 'no',
            'Short Description' => $product->get_short_description(),
            'Description' => $product->get_description(),
            'Regular Price' => $product->get_regular_price(),
            'Sale Price' => $product->get_sale_price(),
            'Stock Status' => $product->get_stock_status(),
            'Stock Quantity' => $product->get_stock_quantity(),
            'Weight' => $product->get_weight(),
            'Length' => $product->get_length(),
            'Width' => $product->get_width(),
            'Height' => $product->get_height(),
            'Date Created' => $product->get_date_created() ? $product->get_date_created()->format('Y-m-d H:i:s') : '',
            'Date Modified' => $product->get_date_modified() ? $product->get_date_modified()->format('Y-m-d H:i:s') : '',
        );
        
        // Add parent information for variations
        if ($parent_product) {
            $data['Parent ID'] = $parent_product->get_id();
            $data['Parent SKU'] = $parent_product->get_sku();
            $data['Parent Name'] = $parent_product->get_name();
        }
        
        // Include categories
        if ($options['include_categories']) {
            $categories = wp_get_post_terms($product->get_id(), 'product_cat', array('fields' => 'names'));
            $data['Categories'] = implode(', ', $categories);
            
            $tags = wp_get_post_terms($product->get_id(), 'product_tag', array('fields' => 'names'));
            $data['Tags'] = implode(', ', $tags);
        }
        
        // Include images
        if ($options['include_images']) {
            $image_ids = $product->get_gallery_image_ids();
            array_unshift($image_ids, $product->get_image_id());
            
            $images = array();
            foreach ($image_ids as $image_id) {
                if ($image_id) {
                    $images[] = wp_get_attachment_url($image_id);
                }
            }
            $data['Images'] = implode(', ', $images);
        }
        
        // Include attributes
        if ($options['include_attributes']) {
            $attributes = $product->get_attributes();
            
            foreach ($attributes as $attribute_name => $attribute) {
                if ($attribute->is_taxonomy()) {
                    $terms = wp_get_post_terms($product->get_id(), $attribute->get_name(), array('fields' => 'names'));
                    $data['Attribute: ' . wc_attribute_label($attribute->get_name())] = implode(', ', $terms);
                } else {
                    $data['Attribute: ' . $attribute->get_name()] = implode(', ', $attribute->get_options());
                }
            }
            
            // For variations, include variation attributes
            if ($product->is_type('variation')) {
                $variation_attributes = $product->get_variation_attributes();
                foreach ($variation_attributes as $attr_name => $attr_value) {
                    $attr_label = wc_attribute_label(str_replace('attribute_', '', $attr_name));
                    $data['Variation: ' . $attr_label] = $attr_value;
                }
            }
            
            // Include contact lens specific attributes if they exist
            $contact_lens_attributes = array(
                'SPH' => 'pa_sph',
                'CYL' => 'pa_cyl', 
                'AXIS' => 'pa_axis',
                'ADD' => 'pa_add',
                'Base Curve' => 'pa_base-curve',
                'Diameter' => 'pa_diameter',
                'Brand' => 'pa_brand',
                'Wear Time' => 'pa_wear-time',
                'Material' => 'pa_material',
                'Color' => 'pa_color',
            );
            
            foreach ($contact_lens_attributes as $label => $attribute_slug) {
                $terms = wp_get_post_terms($product->get_id(), $attribute_slug, array('fields' => 'names'));
                if (!empty($terms) && !is_wp_error($terms)) {
                    $data[$label] = implode(', ', $terms);
                }
            }
        }
        
        // Include custom fields/meta using WooCommerce methods
        $meta_data = $product->get_meta_data();
        $excluded_meta = array(
            '_edit_lock', '_edit_last', '_wp_old_slug', '_wp_old_date',
            '_thumbnail_id', '_product_image_gallery', '_wc_review_count',
            '_wc_rating_count', '_wc_average_rating', '_product_attributes',
            '_default_attributes', '_children', '_price', '_regular_price',
            '_sale_price', '_stock', '_stock_status', '_manage_stock'
        );

        foreach ($meta_data as $meta) {
            $key = $meta->get_data()['key'];
            $value = $meta->get_data()['value'];

            if (strpos($key, '_') === 0 && !in_array($key, $excluded_meta)) {
                $clean_key = 'Meta: ' . ltrim($key, '_');
                $data[$clean_key] = is_array($value) ? implode(', ', $value) : $value;
            }
        }
        
        return apply_filters('wc_product_exporter_product_data', $data, $product, $options);
    }
    
    /**
     * Create temporary directory for exports
     */
    public function create_temp_directory() {
        $upload_dir = wp_upload_dir();
        $temp_dir = $upload_dir['basedir'] . '/wc-product-exporter-temp/';
        
        if (!file_exists($temp_dir)) {
            wp_mkdir_p($temp_dir);
            
            // Create .htaccess to prevent direct access
            $htaccess_content = "Options -Indexes\n<Files *.php>\nOrder allow,deny\nDeny from all\n</Files>";
            file_put_contents($temp_dir . '.htaccess', $htaccess_content);
        }
        
        return $temp_dir;
    }
    
    /**
     * Generate unique filename
     */
    public function generate_filename($format) {
        $timestamp = date('Y-m-d_H-i-s');
        $random = wp_generate_password(8, false);
        return "wc-products-export_{$timestamp}_{$random}.{$format}";
    }
    
    /**
     * Clean old export files
     */
    public function cleanup_old_files() {
        $temp_dir = $this->create_temp_directory();
        $files = glob($temp_dir . 'wc-products-export_*');
        
        foreach ($files as $file) {
            if (is_file($file) && (time() - filemtime($file)) > (24 * 60 * 60)) { // 24 hours
                unlink($file);
            }
        }
    }
}
